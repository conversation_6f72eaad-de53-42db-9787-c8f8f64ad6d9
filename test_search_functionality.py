#!/usr/bin/env python3
"""
测试联网搜索功能
"""

import sys
import os
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

def test_search_api_configuration():
    """测试搜索API配置"""
    print("🧪 测试搜索API配置...")
    
    try:
        from complete_report_generator import CompleteReportGenerator
        
        # 创建生成器实例
        generator = CompleteReportGenerator(use_async=False)
        
        # 创建搜索管理器
        search_manager = generator.SearchManager(generator)
        
        print(f"   配置的搜索API: {list(search_manager.search_apis.keys())}")
        
        for api_name, api_config in search_manager.search_apis.items():
            enabled = api_config.get('enabled', False)
            print(f"   - {api_name}: {'✅ 启用' if enabled else '❌ 禁用'}")
            
        if search_manager.search_apis:
            print("   ✅ 搜索API配置正常")
            return True
        else:
            print("   ❌ 没有配置任何搜索API")
            return False
            
    except Exception as e:
        print(f"   ❌ 测试失败: {str(e)}")
        return False

def test_metaso_search():
    """测试Metaso搜索功能"""
    print("🧪 测试Metaso搜索功能...")
    
    try:
        from complete_report_generator import CompleteReportGenerator
        
        # 创建生成器实例
        generator = CompleteReportGenerator(use_async=False)
        
        # 创建搜索管理器
        search_manager = generator.SearchManager(generator)
        
        # 测试网页搜索
        print("   🌐 测试网页搜索...")
        webpage_results = search_manager.search_metaso("地热发电技术", "webpage", 3)
        print(f"   网页搜索结果数量: {len(webpage_results)}")
        
        if webpage_results:
            for i, result in enumerate(webpage_results[:2], 1):
                print(f"     {i}. {result.get('title', '无标题')[:50]}...")
                print(f"        URL: {result.get('url', '无URL')}")
        
        # 测试学术搜索
        print("   🎓 测试学术搜索...")
        scholar_results = search_manager.search_metaso("地热发电技术研究", "scholar", 3)
        print(f"   学术搜索结果数量: {len(scholar_results)}")
        
        if scholar_results:
            for i, result in enumerate(scholar_results[:2], 1):
                print(f"     {i}. {result.get('title', '无标题')[:50]}...")
                print(f"        URL: {result.get('url', '无URL')}")
        
        if webpage_results or scholar_results:
            print("   ✅ Metaso搜索功能正常")
            return True
        else:
            print("   ❌ Metaso搜索未返回结果")
            return False
            
    except Exception as e:
        print(f"   ❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_multi_source_search():
    """测试多源搜索功能"""
    print("🧪 测试多源搜索功能...")
    
    try:
        from complete_report_generator import CompleteReportGenerator
        
        # 创建生成器实例
        generator = CompleteReportGenerator(use_async=False)
        
        # 创建搜索管理器
        search_manager = generator.SearchManager(generator)
        
        # 测试多源搜索
        query = "地热发电市场规模"
        search_types = ['metaso']  # 只测试Metaso，因为其他API可能未配置
        
        print(f"   🔍 搜索查询: {query}")
        print(f"   📡 搜索源: {search_types}")
        
        results = search_manager.multi_source_search(query, search_types, num_results=5)
        
        print(f"   📊 搜索结果数量: {len(results)}")
        
        if results:
            for i, result in enumerate(results[:3], 1):
                print(f"     {i}. {result.get('title', '无标题')[:60]}...")
                print(f"        来源: {result.get('source', '未知')}")
                print(f"        URL: {result.get('url', '无URL')}")
                print()
            
            print("   ✅ 多源搜索功能正常")
            return True
        else:
            print("   ❌ 多源搜索未返回结果")
            return False
            
    except Exception as e:
        print(f"   ❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_search_enhancement_flow():
    """测试搜索增强流程"""
    print("🧪 测试搜索增强流程...")
    
    try:
        from complete_report_generator import CompleteReportGenerator
        
        # 创建生成器实例
        generator = CompleteReportGenerator(use_async=False)
        
        # 创建模拟报告内容
        mock_report_content = """
        # 地热发电产业研究报告
        
        ## 1. 技术概述
        地热发电是一种利用地热能发电的技术...
        
        ## 2. 市场现状
        当前地热发电市场发展迅速...
        
        ## 3. 发展趋势
        未来地热发电技术将朝着更高效的方向发展...
        """
        
        # 创建临时报告文件
        temp_report_path = "temp_test_report.txt"
        with open(temp_report_path, 'w', encoding='utf-8') as f:
            f.write(mock_report_content)
        
        print(f"   📄 创建临时报告文件: {temp_report_path}")
        
        # 测试内容缺口分析
        search_trigger = generator.SearchTrigger(generator)
        content_gaps = search_trigger.analyze_content_gaps(mock_report_content, "地热发电产业研究")
        
        print(f"   🔍 检测到内容缺口数量: {len(content_gaps)}")
        
        if content_gaps:
            for i, gap in enumerate(content_gaps[:3], 1):
                print(f"     {i}. 类型: {gap.get('type', '未知')}")
                print(f"        原因: {gap.get('reason', '无原因')}")
                print(f"        优先级: {gap.get('priority', '未知')}")
        
        # 测试搜索查询生成
        if content_gaps:
            search_manager = generator.SearchManager(generator)
            first_gap = content_gaps[0]
            queries = search_manager.generate_search_queries("地热发电产业研究", first_gap)
            
            print(f"   📝 为第一个缺口生成的查询: {queries}")
            
            # 测试实际搜索
            if queries:
                test_query = queries[0]
                print(f"   🔍 测试搜索查询: {test_query}")
                
                results = search_manager.multi_source_search(test_query, ['metaso'], num_results=2)
                print(f"   📊 搜索结果数量: {len(results)}")
                
                if results:
                    print("   ✅ 搜索增强流程测试成功")
                    success = True
                else:
                    print("   ⚠️ 搜索未返回结果，但流程正常")
                    success = True
            else:
                print("   ❌ 未生成搜索查询")
                success = False
        else:
            print("   ⚠️ 未检测到内容缺口，但这可能是正常的")
            success = True
        
        # 清理临时文件
        if os.path.exists(temp_report_path):
            os.remove(temp_report_path)
            print(f"   🗑️ 清理临时文件: {temp_report_path}")
        
        return success
        
    except Exception as e:
        print(f"   ❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试联网搜索功能")
    print("=" * 60)
    
    # 测试搜索API配置
    test1_result = test_search_api_configuration()
    print()
    
    # 测试Metaso搜索
    test2_result = test_metaso_search()
    print()
    
    # 测试多源搜索
    test3_result = test_multi_source_search()
    print()
    
    # 测试搜索增强流程
    test4_result = test_search_enhancement_flow()
    print()
    
    # 总结
    print("=" * 60)
    print("📊 测试结果总结:")
    print(f"   搜索API配置: {'✅ 通过' if test1_result else '❌ 失败'}")
    print(f"   Metaso搜索: {'✅ 通过' if test2_result else '❌ 失败'}")
    print(f"   多源搜索: {'✅ 通过' if test3_result else '❌ 失败'}")
    print(f"   搜索增强流程: {'✅ 通过' if test4_result else '❌ 失败'}")
    
    all_passed = all([test1_result, test2_result, test3_result, test4_result])
    
    if all_passed:
        print("\n🎉 所有测试通过！联网搜索功能已修复并正常工作。")
        return True
    else:
        print("\n⚠️ 部分测试失败，请检查具体问题。")
        return False

if __name__ == "__main__":
    main()
